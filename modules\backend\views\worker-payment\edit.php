<?php
use app\common\models\PaymentType;
use app\modules\backend\models\WorkerFinances;
use yii\helpers\Html;
use app\assets\Select2Asset;

// Вычисляем оставшуюся зарплату (учитываем уже выплаченную по этому месяцу)
$salaryPaid = ($paymentAmountsByType[WorkerFinances::TYPE_SALARY] ?? 0)
    + ($paymentAmountsByType[WorkerFinances::TYPE_CASH_SALARY] ?? 0)
    + ($paymentAmountsByType[WorkerFinances::TYPE_ADVANCE] ?? 0);
$remainingSalary = $workerSalary ? max($workerSalary->amount - $salaryPaid, 0) : 0;

Select2Asset::register($this);
?>

<style>
.payment-methods-container {
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.payment-methods-container .form-check {
    margin-bottom: 0;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 120px;
    position: relative;
}

.payment-methods-container .form-check:hover {
    border-color: #007bff;
    box-shadow: 0 1px 3px rgba(0,123,255,0.1);
}

.payment-methods-container .form-check-input {
    margin: 0;
    transform: scale(1.1);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

.payment-methods-container .form-check-label {
    font-weight: 500;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    margin: 0;
    white-space: nowrap;
    text-align: left;
    padding-right: 24px;
}

.payment-methods-container .form-check-input:disabled + .form-check-label {
    opacity: 0.5;
    cursor: not-allowed;
}

.payment-methods-container .form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: 600;
}

/* Стили для основных чекбоксов типов платежей */
.payment-type-row .form-check {
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 150px;
    position: relative;
}

.payment-type-row .form-check:hover {
    background: #e3f2fd;
    border-color: #2196f3;
}

.payment-type-row .form-check-input {
    margin: 0;
    transform: scale(1.2);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

.payment-type-row .form-check-label {
    font-weight: 600;
    font-size: 15px;
    color: #343a40;
    margin: 0;
    white-space: nowrap;
    text-align: left;
    padding-right: 24px;
}

.payment-type-row .form-check-input:checked + .form-check-label {
    color:hsl(207, 89.70%, 54.10%);
}

/* Выравнивание контента в ячейках таблицы */
.payment-type-row td {
    vertical-align: middle;
}

.amount-column {
    vertical-align: middle;
}

/* Стили для полей ввода чисел */
input[inputmode="numeric"] {
    text-align: right;
}

/* Стили для секции погашения долга */
#debt-payment-section .card {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

#debt-payment-section .card-header {
    background:rgb(160, 190, 239);
    border-bottom: 1px solid #dee2e6;
    padding: 12px 15px;
}

#debt-payment-section .form-check {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
    cursor: pointer;
}

#debt-payment-section .form-check:hover {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 8px 12px;
    margin: -8px -12px;
}

#debt-payment-section .form-check-input {
    margin: 0;
    transform: scale(1.2);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

#debt-payment-section .form-check-label {
    font-weight: 350;
    font-size: 15px;
    color:rgb(2, 11, 20);
    margin: 0;
    cursor: pointer;
    padding-right: 30px;
    width: 100%;
}

#debt-payment-section .form-check-input:checked + .form-check-label {
    color: #dc3545;
}

#debt-payment-section .card-body {
    background: white;
    padding: 20px;
}

/* Стили для валидации */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.error-container {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.validation-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.validation-warning {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

/* Стили для панели итогов */
#payment-summary .card-header {
    background: linear-gradient(45deg, #17a2b8, #138496) !important;
}

#payment-summary .card-body {
    background: #f8f9fa;
}

/* Анимация для полей с ошибками */
.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Стили для кнопок */
.btn-lg {
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #545b62);
    border: none;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #545b62, #3d4142);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Улучшенные стили для алертов */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

/* Стили для показа текущих значений */
.current-payment-info {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 10px;
}

.current-payment-info .badge {
    font-size: 12px;
    margin-left: 8px;
}
</style>

<div class="worker-payment-form">

    <form id="worker-payment-edit-form">
        
        <!-- Скрытые поля для передачи данных -->
        <input type="hidden" name="worker_id" value="<?= $selectedWorker->id ?>">
        <input type="hidden" name="month" value="<?= $selectedMonth ?>">
        
        <div class="row mb-3">
            <div class="col-md-8">
                <label for="worker_name"><?= Yii::t('app', 'worker') ?></label>
                <input type="text" id="worker_name" class="form-control" value="<?= Html::encode($selectedWorker->full_name) ?>" readonly>
            </div>
            <div class="col-md-4">
                <label for="month_display"><?= Yii::t('app', 'month') ?></label>
                <input type="text" id="month_display" class="form-control" value="<?= date('Y-m', strtotime($selectedMonth . '-01')) ?>" readonly>
            </div>
        </div>

       

        <!-- Worker Info Panel -->
        <div id="worker-info" class="alert alert-info">
            <div class="row">
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'salary') ?>:</strong> <span id="worker-salary"><?= $workerSalary ? number_format($workerSalary->amount, 0, ',', ' ') : '0' ?></span>
                </div>
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'current_debt') ?>:</strong> <span id="worker-debt-amount"><?= number_format($workerDebt, 0, ',', ' ') ?></span>
                </div>
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'remaining_salary') ?>:</strong> <span id="worker-remaining-salary"><?= number_format($remainingSalary, 0, ',', ' ') ?></span>
                </div>
            </div>
        </div>


        <!-- Payment Types Selection -->
        <div class="form-group">
            <label><?= Yii::t('app', 'payment_types') ?></label>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th width="40%"><?= Yii::t('app', 'payment_type') ?></th>
                            <th width="30%"><?= Yii::t('app', 'payment_method') ?></th>
                            <th width="30%"><?= Yii::t('app', 'amount') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($paymentTypes as $typeId => $typeName): ?>
                            <?php if ($typeId != WorkerFinances::TYPE_DEBT_PAYMENT && $typeId != WorkerFinances::TYPE_CASH_SALARY): // Исключаем долг и наличную зарплату ?>
                                <?php 
                                $existingAmount = $paymentAmountsByType[$typeId] ?? 0;
                                $methodAmounts = $paymentMethodsByType[$typeId] ?? [];
                                $isChecked = $existingAmount > 0;
                                $multipleMethods = count($methodAmounts) > 1;
                                ?>
                                <tr class="payment-type-row" data-type="<?= $typeId ?>">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input payment-type-checkbox" type="checkbox" 
                                                   id="payment_type_<?= $typeId ?>" value="<?= $typeId ?>"
                                                   <?= $isChecked ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="payment_type_<?= $typeId ?>">
                                                <?= $typeName ?>
                                            </label>
                                        </div>
                                        <!-- Текущая сумма скрыта по требованию -->
                                    </td>
                                    <td>
                                        <div class="payment-methods-container">
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox" 
                                                       id="cash_<?= $typeId ?>" value="<?= PaymentType::CASH ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" 
                                                       <?= isset($methodAmounts[PaymentType::CASH]) ? 'checked' : '' ?>
                                                       <?= $isChecked ? '' : 'disabled' ?>>
                                                <label class="form-check-label" for="cash_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'cash') ?>
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox" 
                                                       id="card_<?= $typeId ?>" value="<?= PaymentType::PAYMENT_CARD ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" 
                                                       <?= isset($methodAmounts[PaymentType::PAYMENT_CARD]) ? 'checked' : '' ?>
                                                       <?= $isChecked ? '' : 'disabled' ?>>
                                                <label class="form-check-label" for="card_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'payment_card') ?>
                                                </label>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="amount-column">
                                        <input type="text"
                                               class="form-control amount-input"
                                               name="payment_types[<?= $typeId ?>][amount]"
                                               placeholder="<?= Yii::t('app', 'amount') ?>"
                                               inputmode="numeric"
                                               pattern="[0-9\s]*"
                                               style="<?= $multipleMethods ? 'display:none;' : '' ?>"
                                               value="<?= !$multipleMethods && $existingAmount > 0 ? number_format($existingAmount, 0, ',', ' ') : '' ?>"
                                               <?= $isChecked ? '' : 'disabled' ?>>
                                        <div class="dynamic-amounts" style="<?= $multipleMethods ? '' : 'display: none;' ?>">
                                            <!-- Dynamic inputs will be added here -->
                                        </div>
                                        <div class="error-container"></div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Debt Payment Section -->
        <?php 
        $debtPaymentAmount = $paymentAmountsByType[WorkerFinances::TYPE_DEBT_PAYMENT] ?? 0;
        $hasDebtPayment = $debtPaymentAmount > 0;
        ?>
        <div id="debt-payment-section" class="form-group" <?= $workerDebt > 0 || $hasDebtPayment ? '' : 'style="display: none;"' ?>>
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="debt-payment-checkbox" <?= $hasDebtPayment ? 'checked' : '' ?>>
                        <label class="form-check-label" for="debt-payment-checkbox">
                            <strong><?= $paymentTypes[WorkerFinances::TYPE_DEBT_PAYMENT] ?? 'Погашение долга' ?></strong>
                        </label>
                    </div>
                    <!-- Блок текущей суммы скрыт по требованию -->
                </div>
                <div class="card-body" id="debt-payment-details" <?= $hasDebtPayment ? '' : 'style="display: none;"' ?>>
                    <div class="row">
                        <div class="col-md-12">
                            <label><?= Yii::t('app', 'amount') ?></label>
                            <input type="text" class="form-control" id="debt-payment-amount"
                                   name="payment_types[<?= WorkerFinances::TYPE_DEBT_PAYMENT ?>][amount]"
                                   inputmode="numeric" pattern="[0-9\s]*" 
                                   value="<?= $hasDebtPayment ? number_format($debtPaymentAmount, 0, '', '') : '' ?>"
                                   <?= $hasDebtPayment ? '' : 'disabled' ?>>
                            <small class="text-muted"><?= Yii::t('app', 'debt_amount') ?>: <span id="worker-debt-total"><?= number_format($workerDebt, 0, ',', ' ') ?></span></small>
                            <div class="error-container"></div>
                        </div>
                    </div>
                </div>
        </div>

    </form>
</div>

<script>
// Константы для JavaScript (защитное объявление, чтобы не дублировать при повторных загрузках модального окна)
(function (w) {
    if (typeof w.WORKER_FINANCES_TYPE_SALARY === 'undefined') {
        w.WORKER_FINANCES_TYPE_SALARY   = 1;
        w.WORKER_FINANCES_TYPE_ADVANCE  = 2;
        w.WORKER_FINANCES_TYPE_DEBT_PAYMENT = 6;
        w.WORKER_FINANCES_TYPE_CASH_SALARY = 8;

        w.PAYMENT_TYPE_CASH        = 1;
        w.PAYMENT_TYPE_TRANSFER    = 2;
        w.PAYMENT_TYPE_TERMINAL    = 3;
        w.PAYMENT_TYPE_PAYMENT_CARD = 4;
    }

    // Данные для редактирования (перезаписываем при каждом открытии формы)
    w.EDIT_MODE      = true;
    w.WORKER_ID      = <?= $selectedWorker->id ?>;
    w.EDIT_MONTH     = '<?= $selectedMonth ?>';
    w.WORKER_SALARY  = <?= $workerSalary ? $workerSalary->amount : 0 ?>;
    w.WORKER_DEBT    = <?= $workerDebt ?>;

    // Суммы выплат по методам оплаты для каждого типа
    w.PAYMENT_METHODS_BY_TYPE = <?= json_encode($paymentMethodsByType ?? []) ?>;
})(window);
</script>

<script>
// Заполняем суммы по методам после инициализации UI
$(document).ready(function () {
    setTimeout(function () {
        Object.keys(PAYMENT_METHODS_BY_TYPE).forEach(function (typeId) {
            var amounts = PAYMENT_METHODS_BY_TYPE[typeId];
            var row = $('.payment-type-row[data-type="' + typeId + '"]');

            // Если более одного метода, убеждаемся, что динамические поля созданы
            if (Object.keys(amounts).length > 1) {
                row.find('.payment-method-checkbox:checked').first().trigger('change');
            }

            // Заполняем поля
            for (var methodId in amounts) {
                if (!amounts.hasOwnProperty(methodId)) continue;
                var amountVal = amounts[methodId];
                var formatted = (typeof window.WorkerPaymentEditCalculations !== 'undefined') ? window.WorkerPaymentEditCalculations.formatNumber(amountVal) : amountVal;
                var input = row.find('input.amount-input[data-method="' + methodId + '"]');

                if (input.length) {
                    input.val(formatted);
                } else {
                    // Если только один метод, основное поле
                    row.find('input.amount-input').not('[data-method]').val(formatted);
                }
            }
        });
    }, 100); // небольшая задержка, чтобы UI успел создаться
});
</script>

<script>
// ===== ПОЛНЫЙ JAVASCRIPT КОД ДЛЯ ФОРМЫ РЕДАКТИРОВАНИЯ ПЛАТЕЖЕЙ =====
// Все обработчики событий и логика в одном месте для решения проблемы с Backspace

$(document).ready(function() {
    console.log('🔍 Инициализация JavaScript для формы редактирования платежей');

    // Переменная для отслеживания операций удаления
    var isDeleteOperation = false;

    // ===== ОБРАБОТЧИКИ КЛАВИАТУРЫ =====

    // Обработчик keydown для отслеживания операций удаления
    $(document).on('keydown', '#worker-payment-edit-form input', function (e) {
        var keyCode = e.which || e.keyCode;
        var $currentElement = $(this);

        console.log('🔍 KeyDown:', keyCode, 'в поле:', $currentElement.attr('class'));

        if (keyCode === 8 || keyCode === 46) { // Backspace или Delete
            isDeleteOperation = true;
            console.log('🔍 Обнаружена операция удаления, отключаем автоперераспределение');
        }
    });

    // Обработчик keyup для сброса флага операции удаления
    $(document).on('keyup', '#worker-payment-edit-form input', function (e) {
        var keyCode = e.which || e.keyCode;

        if (keyCode === 8 || keyCode === 46) { // Backspace или Delete
            setTimeout(function () {
                isDeleteOperation = false;
                console.log('🔍 Операция удаления завершена, включаем автоперераспределение');
            }, 50);
        }
    });

    // ===== ФУНКЦИИ РАСЧЕТОВ =====

    // Форматирование чисел с разделителями тысяч
    function formatNumberInput(value) {
        console.log('🔧 formatNumberInput:', value);
        if (value === null || value === undefined || value === '') return '';

        try {
            var numericValue = value.toString().replace(/[^\d]/g, '');
            if (numericValue === '') return '';
            return parseInt(numericValue, 10).toLocaleString('ru-RU');
        } catch (e) {
            console.warn('Error formatting number input:', value, e);
            return '';
        }
    }

    // Получение числового значения из строки
    function getNumericValue(value) {
        if (!value) return 0;
        return parseInt(value.toString().replace(/[^\d]/g, ''), 10) || 0;
    }

    // Обновление основного поля суммы на основе динамических полей
    function updateMainAmountFromDynamicInputs(paymentRow) {
        console.log('🔧 updateMainAmountFromDynamicInputs');
        var dynamicInputs = paymentRow.find('.dynamic-amounts input[data-method]');
        var total = 0;

        dynamicInputs.each(function() {
            var value = getNumericValue($(this).val());
            total += value;
        });

        var mainInput = paymentRow.find('.amount-input').not('[data-method]');
        if (mainInput.length) {
            mainInput.val(total > 0 ? formatNumberInput(total.toString()) : '');
        }

        console.log('🔧 Обновлена основная сумма:', total);
    }

    // Упрощённое распределение остатка
    function simpleAutoDistribute(changedInput) {
        console.log('🔧 simpleAutoDistribute вызван для поля:', {
            class: changedInput.attr('class'),
            value: changedInput.val(),
            dataMethod: changedInput.attr('data-method')
        });

        var paymentRow = changedInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');

        console.log('🔧 Тип платежа:', paymentTypeId);

        // Для типов платежей, отличных от зарплаты и аванса, не делаем автоматическое распределение
        if (paymentTypeId != WORKER_FINANCES_TYPE_SALARY && paymentTypeId != WORKER_FINANCES_TYPE_ADVANCE) {
            console.log('🔧 Тип платежа не зарплата/аванс - только пересчитываем общую сумму');
            updateMainAmountFromDynamicInputs(paymentRow);
            return;
        }

        // Для зарплаты и аванса - автоматическое распределение
        var enteredAmount = getNumericValue(changedInput.val());
        var changedMethodId = changedInput.attr('data-method');

        // Получаем все динамические поля в этой строке
        var dynamicInputs = paymentRow.find('.dynamic-amounts input[data-method]');
        var otherInputs = dynamicInputs.not(changedInput);

        if (otherInputs.length === 0) {
            updateMainAmountFromDynamicInputs(paymentRow);
            return;
        }

        // Получаем лимиты
        var originalSalary = WORKER_SALARY || 0;
        var maxAllowedAmount = originalSalary; // Упрощенный расчет для примера

        // Распределяем остаток между другими полями
        var remainingAmount = Math.max(0, maxAllowedAmount - enteredAmount);
        var otherInputsCount = otherInputs.length;
        var amountPerOther = otherInputsCount > 0 ? Math.floor(remainingAmount / otherInputsCount) : 0;

        console.log('🔧 Распределение:', {
            enteredAmount: enteredAmount,
            remainingAmount: remainingAmount,
            amountPerOther: amountPerOther
        });

        // Заполняем другие поля
        otherInputs.each(function() {
            $(this).val(amountPerOther > 0 ? formatNumberInput(amountPerOther.toString()) : '');
        });

        // Обновляем основное поле
        updateMainAmountFromDynamicInputs(paymentRow);
    }

    // ===== ОСНОВНОЙ ОБРАБОТЧИК INPUT =====

    // Обработчик input событий для всех числовых полей
    $(document).on('input', '#worker-payment-edit-form input[inputmode="numeric"], #worker-payment-edit-form .amount-input', function () {
        var $this = $(this);
        var value = $this.val();

        console.log('🔍 INPUT EVENT:', {
            class: $this.attr('class'),
            value: value,
            isDeleteOperation: isDeleteOperation
        });

        // Очищаем все символы, кроме цифр, для последующего форматирования
        value = value.replace(/[^\d]/g, '');

        // Форматируем число с разделителями тысяч
        var formattedValue = formatNumberInput(value);
        $this.val(formattedValue);

        // Логика обработки в зависимости от типа поля
        if ($this.hasClass('amount-input') && !$this.attr('data-dynamic')) {
            // Основное поле суммы типа платежа
            console.log('🔍 Обработка основного поля суммы');

        } else if ($this.attr('data-dynamic') === '1' || $this.closest('.dynamic-amounts').length > 0) {
            // Динамическое поле в .dynamic-amounts
            console.log('🔍 Обработка динамического поля, isDeleteOperation:', isDeleteOperation);

            // НЕ вызываем автоперераспределение при операциях удаления
            if (!isDeleteOperation) {
                console.log('🔍 Вызываем simpleAutoDistribute');
                simpleAutoDistribute($this);
            } else {
                console.log('🔍 Пропускаем автоперераспределение из-за операции удаления');
                // Только обновляем основное поле суммы без перераспределения
                var paymentRow = $this.closest('tr');
                updateMainAmountFromDynamicInputs(paymentRow);
            }

        } else if ($this.attr('id') === 'debt-payment-amount') {
            // Поле погашения долга
            console.log('🔍 Обработка поля погашения долга');
        }
    });

    // ===== ОБРАБОТЧИКИ ЧЕКБОКСОВ =====

    // Обработчик изменения чекбоксов типов платежей
    $(document).on('change', '.payment-type-checkbox', function () {
        var $this = $(this);
        var row = $this.closest('tr');
        var paymentMethods = row.find('.payment-method-checkbox');
        var amountInput = row.find('.amount-input');
        var dynamicAmounts = row.find('.dynamic-amounts');

        if ($this.prop('checked')) {
            // Включаем методы оплаты и поле суммы
            paymentMethods.prop('disabled', false);
            amountInput.prop('disabled', false);
        } else {
            // Отключаем методы оплаты и очищаем поля
            paymentMethods.prop('disabled', true).prop('checked', false);
            amountInput.prop('disabled', true).val('');
            dynamicAmounts.hide().empty();
        }
    });

    // Обработчик изменения чекбоксов методов оплаты
    $(document).on('change', '.payment-method-checkbox', function () {
        var $this = $(this);
        var row = $this.closest('tr');
        var checkedMethods = row.find('.payment-method-checkbox:checked');
        var mainAmountInput = row.find('.amount-input').not('[data-method]');
        var dynamicAmounts = row.find('.dynamic-amounts');

        if (checkedMethods.length > 1) {
            // Несколько методов - показываем динамические поля
            mainAmountInput.hide();
            dynamicAmounts.show();

            // Создаем поля для каждого выбранного метода
            dynamicAmounts.empty();
            checkedMethods.each(function() {
                var methodId = $(this).val();
                var methodName = $(this).next('label').text();

                var inputHtml = '<div class="form-group mb-2">' +
                    '<label class="form-label">' + methodName + ':</label>' +
                    '<input type="text" class="form-control amount-input" ' +
                    'data-dynamic="1" data-method="' + methodId + '" ' +
                    'inputmode="numeric" pattern="[0-9\\s]*">' +
                    '</div>';

                dynamicAmounts.append(inputHtml);
            });

        } else if (checkedMethods.length === 1) {
            // Один метод - показываем основное поле
            dynamicAmounts.hide().empty();
            mainAmountInput.show();

        } else {
            // Нет выбранных методов - скрываем все
            dynamicAmounts.hide().empty();
            mainAmountInput.show().val('');
        }
    });

    // Обработчик чекбокса погашения долга
    $(document).on('change', '#debt-payment-checkbox', function () {
        var $this = $(this);
        var details = $('#debt-payment-details');
        var amountInput = $('#debt-payment-amount');

        if ($this.prop('checked')) {
            details.show();
            amountInput.prop('disabled', false);
        } else {
            details.hide();
            amountInput.prop('disabled', true).val('');
        }
    });

    // ===== ОБРАБОТЧИКИ КЛИКОВ =====

    // Делаем весь контейнер кликабельным для чекбоксов
    $(document).on('click', '.form-check', function (e) {
        if (!$(e.target).is('input[type="checkbox"], label')) {
            var checkbox = $(this).find('input[type="checkbox"]');
            if (!checkbox.prop('disabled')) {
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        }
    });

    // ===== ОБРАБОТЧИКИ МОДАЛЬНОГО ОКНА И КНОПОК =====

    // Обработчик отправки формы редактирования
    $(document).on('submit', '#worker-payment-edit-form', function (e) {
        e.preventDefault();
        console.log('🔍 Отправка формы редактирования');

        // Здесь будет логика валидации и отправки
        // Пока просто предотвращаем стандартную отправку
        return false;
    });

    // Обработчик кнопки обновления платежа
    $(document).on('click', '.worker-payment-update-button', function (e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🔍 Нажата кнопка обновления платежа');

        // Предотвращаем закрытие модального окна
        if ($(this).prop('disabled')) {
            return false;
        }

        // Блокируем кнопку на время обработки
        $(this).prop('disabled', true);

        // Собираем данные формы
        var formData = $('#worker-payment-edit-form').serialize();
        console.log('🔍 Данные формы:', formData);

        // Здесь будет AJAX запрос для обновления
        // Пока просто имитируем успешное обновление
        setTimeout(function() {
            $('.worker-payment-update-button').prop('disabled', false);
            console.log('🔍 Обновление завершено (имитация)');

            // Закрываем модальное окно и обновляем таблицу
            $('.close').trigger('click');
            if (typeof $.pjax !== 'undefined') {
                $.pjax.reload({
                    container: '#worker-payment-grid-pjax',
                    timeout: false
                });
            }
        }, 1000);

        return false;
    });

    // Предотвращаем закрытие модального окна при клике на кнопки внутри формы
    $(document).on('click', '#worker-payment-edit-form button, #worker-payment-edit-form input[type="button"]', function (e) {
        e.stopPropagation();
        console.log('🔍 Клик по кнопке в форме, предотвращаем закрытие модального окна');
    });

    // Предотвращаем закрытие модального окна при клике на элементы формы
    $(document).on('click', '#worker-payment-edit-form', function (e) {
        e.stopPropagation();
    });

    // Обработчик для предотвращения случайного закрытия модального окна
    $(document).on('click', '#ideal-mini-modal .modal-dialog', function (e) {
        e.stopPropagation();
    });

    console.log('🔍 JavaScript для формы редактирования платежей инициализирован');
});
</script>
