/**
 * Модуль обработчиков событий для формы редактирования платежа работнику
 * Содержит все обработчики событий формы редактирования
 */

// Глобальный объект для обработчиков событий редактирования
window.WorkerPaymentEditHandlers = {

    // Переменная для отслеживания операций удаления
    isDeleteOperation: false,

    /**
     * Инициализация всех обработчиков событий для редактирования
     */
    init: function () {
        this.initClickHandlers();
        this.initChangeHandlers();
        this.initInputHandlers();
        this.initKeyboardHandlers(); // Добавляем обработчики клавиатуры
        this.initFormSubmitHandler();
        this.initValidationHandlers();

        // NEW: Приводим UI в корректное состояние для уже выбранных типов/секций при загрузке формы
        $('.payment-type-checkbox:checked').each(function () {
            $(this).trigger('change');
        });
        $('#debt-payment-checkbox:checked').trigger('change');

        // Триггерим изменение для уже отмеченных методов оплаты
        $('.payment-method-checkbox:checked').each(function () {
            $(this).trigger('change');
        });
    },

    /**
     * Обработчики кликов для формы редактирования
     */
    initClickHandlers: function () {
        // Делаем весь контейнер кликабельным для чекбокса погашения долга
        $('#debt-payment-section .form-check').on('click', function (e) {
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });

        // Делаем весь контейнер кликабельным для чекбоксов типов платежей
        $('.payment-type-row .form-check').on('click', function (e) {
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });

        // Делаем весь контейнер кликабельным для чекбоксов методов оплаты
        $('.payment-methods-container .form-check').on('click', function (e) {
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                var row = $(this).closest('tr');
                var typeCheckbox = row.find('.payment-type-checkbox');

                if (!typeCheckbox.prop('checked')) {
                    typeCheckbox.prop('checked', true).trigger('change');
                }

                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });
    },

    /**
     * Обработчики изменений для формы редактирования
     */
    initChangeHandlers: function () {
        // Обработчик изменения чекбоксов типов платежей
        $('.payment-type-checkbox').on('change', function () {
            var $this = $(this);
            var row = $this.closest('tr');
            var paymentMethods = row.find('.payment-method-checkbox');
            var amountInput = row.find('.amount-input');
            var dynamicAmounts = row.find('.dynamic-amounts');

            if ($this.prop('checked')) {
                // Включаем методы оплаты и поле суммы
                paymentMethods.prop('disabled', false);
                amountInput.prop('disabled', false);

                // Автоматически выбираем наличные, если ничего не выбрано
                if (!paymentMethods.filter(':checked').length) {
                    paymentMethods.first().prop('checked', true).trigger('change');
                }
            } else {
                // Отключаем и сбрасываем
                paymentMethods.prop('disabled', true).prop('checked', false);
                amountInput.prop('disabled', true).val('');
                dynamicAmounts.hide().empty();

                // Очищаем ошибки валидации
                if (typeof window.WorkerPaymentEditUI !== 'undefined') {
                    window.WorkerPaymentEditUI.clearFieldErrors(amountInput);
                }
            }
        });

        // Обработчик изменения чекбоксов методов оплаты
        $('.payment-method-checkbox').on('change', function () {
            var row = $(this).closest('tr');
            var checkedMethods = row.find('.payment-method-checkbox:checked');
            var amountInput = row.find('.amount-input');
            var dynamicAmounts = row.find('.dynamic-amounts');

            if (checkedMethods.length > 1) {
                // Несколько методов - показываем отдельные поля
                amountInput.hide();
                dynamicAmounts.show();

                if (typeof window.WorkerPaymentEditUI !== 'undefined') {
                    window.WorkerPaymentEditUI.createDynamicAmountInputs(row, checkedMethods);
                }

                // Автоматическое распределение суммы между методами оплаты, как в форме создания
                if (typeof window.WorkerPaymentEditCalculations !== 'undefined') {
                    window.WorkerPaymentEditCalculations.autoDistributeToMethods(row);
                }
            } else if (checkedMethods.length === 1) {
                // Один метод - показываем основное поле
                amountInput.show();
                dynamicAmounts.hide().empty();
            } else {
                // Ни одного метода не выбрано
                amountInput.show().val('');
                dynamicAmounts.hide().empty();
            }
        });

        // Обработчик чекбокса погашения долга
        $('#debt-payment-checkbox').on('change', function () {
            var $this = $(this);
            var details = $('#debt-payment-details');
            var amountInput = $('#debt-payment-amount');

            if ($this.prop('checked')) {
                details.show();
                amountInput.prop('disabled', false);
            } else {
                details.hide();
                amountInput.prop('disabled', true).val('');
            }
        });
    },

    /**
     * Обработчики клавиатуры для формы редактирования
     */
    initKeyboardHandlers: function () {
        console.log('Инициализация обработчиков клавиатуры для формы редактирования платежей');

        // Обработчик событий клавиатуры для всех полей ввода в форме
        $(document).on('keydown', '#worker-payment-edit-form input', function (e) {
            var $currentElement = $(this);
            var keyCode = e.which || e.keyCode;

            console.log('=== KEYBOARD EVENT DEBUG ===');
            console.log('Нажата клавиша:', keyCode, '(' + e.key + ')');
            console.log('Элемент в фокусе:', {
                id: $currentElement.attr('id') || 'без id',
                class: $currentElement.attr('class') || 'без класса',
                type: $currentElement.attr('type') || 'без типа',
                name: $currentElement.attr('name') || 'без имени',
                value: $currentElement.val(),
                tagName: $currentElement.prop('tagName')
            });

            // Отслеживание операций удаления для автоперераспределения
            if (keyCode === 8 || keyCode === 46) { // Backspace или Delete
                self.isDeleteOperation = true;
                console.log('🔍 Обнаружена операция удаления, отключаем автоперераспределение, isDeleteOperation =', self.isDeleteOperation);
            }

            // Специальная обработка клавиши Backspace (код 8)
            if (keyCode === 8) {
                console.log('🔍 ОБРАБОТКА BACKSPACE:');
                console.log('Текущее значение поля:', $currentElement.val());
                console.log('Позиция курсора:', $currentElement[0].selectionStart, '-', $currentElement[0].selectionEnd);
                console.log('Длина текста:', $currentElement.val().length);
                console.log('Поле активно/в фокусе:', $currentElement.is(':focus'));
                console.log('Поле заблокировано:', $currentElement.prop('disabled'));
                console.log('Поле только для чтения:', $currentElement.prop('readonly'));

                // Проверяем, есть ли выделенный текст
                var selectionStart = $currentElement[0].selectionStart;
                var selectionEnd = $currentElement[0].selectionEnd;
                var hasSelection = selectionStart !== selectionEnd;

                console.log('Есть выделенный текст:', hasSelection);
                if (hasSelection) {
                    console.log('Выделенный текст:', $currentElement.val().substring(selectionStart, selectionEnd));
                }

                // Логируем родительские элементы для понимания контекста
                console.log('Родительские элементы:');
                var $parent = $currentElement.parent();
                var level = 0;
                while ($parent.length && level < 3) {
                    console.log('  Уровень', level + ':', {
                        tagName: $parent.prop('tagName'),
                        class: $parent.attr('class') || 'без класса',
                        id: $parent.attr('id') || 'без id'
                    });
                    $parent = $parent.parent();
                    level++;
                }

                // Проверяем, есть ли специальные обработчики для этого поля
                var hasInputHandler = $currentElement.hasClass('amount-input') ||
                    $currentElement.attr('inputmode') === 'numeric' ||
                    $currentElement.attr('data-dynamic') === '1';
                console.log('Поле имеет специальные обработчики ввода:', hasInputHandler);

                // Логируем состояние формы
                console.log('Состояние формы:');
                console.log('  Всего полей ввода:', $('#worker-payment-edit-form input').length);
                console.log('  Активных чекбоксов типов платежей:', $('.payment-type-checkbox:checked').length);
                console.log('  Активных чекбоксов методов оплаты:', $('.payment-method-checkbox:checked').length);

                // Проверяем, не вызовет ли Backspace какие-то побочные эффекты
                try {
                    // Сохраняем состояние до обработки
                    var valueBefore = $currentElement.val();
                    var cursorBefore = $currentElement[0].selectionStart;

                    console.log('Состояние ДО обработки Backspace:');
                    console.log('  Значение:', valueBefore);
                    console.log('  Позиция курсора:', cursorBefore);

                    // Позволяем браузеру обработать Backspace естественным образом
                    // Через небольшую задержку проверим результат
                    setTimeout(function () {
                        console.log('Состояние ПОСЛЕ обработки Backspace:');
                        console.log('  Значение:', $currentElement.val());
                        console.log('  Позиция курсора:', $currentElement[0].selectionStart);
                        console.log('  Изменилось ли значение:', valueBefore !== $currentElement.val());

                        // Проверяем, не произошло ли неожиданных изменений в других полях
                        var allInputs = $('#worker-payment-edit-form input[type="text"], #worker-payment-edit-form input[inputmode="numeric"]');
                        console.log('Проверка других полей формы после Backspace:');
                        allInputs.each(function (index) {
                            var $input = $(this);
                            if ($input[0] !== $currentElement[0]) {
                                console.log('  Поле', index + ':', {
                                    id: $input.attr('id') || 'без id',
                                    value: $input.val(),
                                    focused: $input.is(':focus')
                                });
                            }
                        });

                        console.log('=== КОНЕЦ ОБРАБОТКИ BACKSPACE ===');
                    }, 10);

                } catch (error) {
                    console.error('Ошибка при обработке Backspace:', error);
                    console.error('Stack trace:', error.stack);
                }
            }

            // Логируем другие важные клавиши
            if (keyCode === 13) { // Enter
                console.log('🔍 НАЖАТА КЛАВИША ENTER');
                console.log('Элемент:', $currentElement.attr('id') || $currentElement.attr('class'));
            }

            if (keyCode === 9) { // Tab
                console.log('🔍 НАЖАТА КЛАВИША TAB');
                console.log('Shift нажат:', e.shiftKey);
            }

            if (keyCode === 27) { // Escape
                console.log('🔍 НАЖАТА КЛАВИША ESCAPE');
            }

            console.log('=== END KEYBOARD EVENT ===');
        });

        // Дополнительный обработчик для события keyup (после обработки клавиши)
        $(document).on('keyup', '#worker-payment-edit-form input', function (e) {
            var keyCode = e.which || e.keyCode;

            if (keyCode === 8) { // Backspace
                var $currentElement = $(this);
                console.log('🔍 KEYUP ПОСЛЕ BACKSPACE:');
                console.log('Финальное значение поля:', $currentElement.val());
                console.log('Финальная позиция курсора:', $currentElement[0].selectionStart);
            }

            // Сброс флага операции удаления
            if (keyCode === 8 || keyCode === 46) { // Backspace или Delete
                setTimeout(function () {
                    self.isDeleteOperation = false;
                    console.log('🔍 Операция удаления завершена, включаем автоперераспределение, isDeleteOperation =', self.isDeleteOperation);
                }, 50);
            }
        });

        console.log('Обработчики клавиатуры инициализированы');
    },

    /**
     * Обработчики ввода для формы редактирования
     */
    initInputHandlers: function () {
        var self = this;

        console.log('🔍 initInputHandlers ВЫЗВАН для формы редактирования!');

        // Отладка: проверяем, какие поля попадают под селектор
        console.log('🔍 Проверка полей для обработчика input:');
        console.log('  input[inputmode="numeric"]:', $('input[inputmode="numeric"]').length);
        console.log('  .amount-input:', $('.amount-input').length);
        console.log('  input[data-dynamic="1"]:', $('input[data-dynamic="1"]').length);
        console.log('  Общий селектор:', $('input[inputmode="numeric"], .amount-input, input[data-dynamic="1"]').length);

        // Универсальный обработчик для отладки - отслеживаем ВСЕ input события в форме
        $(document).on('input', '#worker-payment-edit-form input', function () {
            var $this = $(this);
            console.log('🔍 УНИВЕРСАЛЬНЫЙ INPUT EVENT:', {
                id: $this.attr('id') || 'без id',
                class: $this.attr('class'),
                inputmode: $this.attr('inputmode'),
                dataDynamic: $this.attr('data-dynamic'),
                value: $this.val(),
                hasAmountInputClass: $this.hasClass('amount-input'),
                isInDynamicAmounts: $this.closest('.dynamic-amounts').length > 0
            });
        });

        // Форматирование числовых полей при вводе
        $(document).on('input', 'input[inputmode="numeric"], .amount-input, input[data-dynamic="1"]', function () {
            var $this = $(this);
            var value = $this.val();

            console.log('🔍 INPUT EVENT TRIGGERED!');
            console.log('🔍 Поле:', {
                id: $this.attr('id') || 'без id',
                class: $this.attr('class'),
                inputmode: $this.attr('inputmode'),
                dataDynamic: $this.attr('data-dynamic'),
                value: value
            });
            console.log('🔍 Input event, isDeleteOperation:', self.isDeleteOperation);

            // Очищаем все символы, кроме цифр, для последующего форматирования
            value = value.replace(/[^\d]/g, '');

            if (typeof window.WorkerPaymentEditCalculations !== 'undefined') {
                // Форматируем число с разделителями тысяч
                var formattedValue = window.WorkerPaymentEditCalculations.formatNumberInput(value);
                $this.val(formattedValue);

                // Дополнительная логика распределения и лимитов
                if ($this.hasClass('amount-input') && !$this.attr('data-dynamic')) {
                    // основное поле
                    window.WorkerPaymentEditCalculations.handleMainAmountInput($this);
                } else if ($this.attr('data-dynamic') === '1' || $this.closest('.dynamic-amounts').length > 0) {
                    // динамическое поле в .dynamic-amounts
                    console.log('🔍 Обработка динамического поля, isDeleteOperation:', self.isDeleteOperation);
                    // НЕ вызываем автоперераспределение при операциях удаления
                    if (!self.isDeleteOperation) {
                        console.log('🔍 Вызываем simpleAutoDistribute');
                        window.WorkerPaymentEditCalculations.simpleAutoDistribute($this);
                    } else {
                        console.log('🔍 Пропускаем автоперераспределение из-за операции удаления');
                        // Только обновляем основное поле суммы без перераспределения
                        var paymentRow = $this.closest('tr');
                        window.WorkerPaymentEditCalculations.updateMainAmountFromDynamicInputs(paymentRow);
                    }
                } else if ($this.attr('id') === 'debt-payment-amount') {
                    if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                        window.WorkerPaymentEditValidation.validateDebtAmount();
                    }
                }
            }

            // Пересчитываем общую сумму (можно подключить при необходимости)
            // var total = window.WorkerPaymentEditCalculations.calculateTotalAmount();
        });

        // Обработчик потери фокуса для валидации
        $(document).on('blur', 'input[inputmode="numeric"]', function () {
            if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                window.WorkerPaymentEditValidation.validateAmount($(this));
            }
        });

        // Преобразуем отформатированные значения обратно в числа перед отправкой формы
        $('#worker-payment-edit-form').on('submit', function () {
            $('input[inputmode="numeric"]').each(function () {
                if (typeof window.WorkerPaymentEditCalculations !== 'undefined') {
                    var numericValue = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                    $(this).val(numericValue);
                }
            });
        });
    },

    /**
     * Обработчик отправки формы редактирования
     */
    initFormSubmitHandler: function () {
        // Валидация при отправке формы редактирования
        $('#worker-payment-edit-form').on('submit', function (e) {
            e.preventDefault();

            if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                if (!window.WorkerPaymentEditValidation.validateForm()) {
                    return false;
                }
            }

            // Подготавливаем данные для отправки
            var formData;
            if (typeof window.WorkerPaymentEditAjax !== 'undefined') {
                formData = window.WorkerPaymentEditAjax.prepareFormData();
                // Отправляем данные на сервер
                window.WorkerPaymentEditAjax.updatePayment(formData);
            }
        });

        // Обработчик клика по кнопке обновления
        $(document).off('click.worker-payment-update').on('click.worker-payment-update', '.worker-payment-update-button', function (e) {
            e.preventDefault();
            $('#worker-payment-edit-form').trigger('submit');
        });
    },

    /**
     * Обработчики валидации для формы редактирования
     */
    initValidationHandlers: function () {
        // Привязываем валидацию к событиям
        $(document).on('input change', '.payment-amount, .payment-type-checkbox, .payment-method-checkbox', function () {
            // Задержка для избежания частых вызовов
            clearTimeout(window.editValidationTimeout);
            window.editValidationTimeout = setTimeout(function () {
                if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                    window.WorkerPaymentEditValidation.validateForm();
                }
            }, 300);
        });

        // Специальный обработчик для поля погашения долга
        $(document).on('input', '#debt-payment-amount', function () {
            if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                window.WorkerPaymentEditValidation.validateDebtAmount();
            }
        });
    }
};
