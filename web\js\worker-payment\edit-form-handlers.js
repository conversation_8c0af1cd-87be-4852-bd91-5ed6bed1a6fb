/**
 * Модуль обработчиков событий для формы редактирования платежа работнику
 * Содержит все обработчики событий формы редактирования
 */

// Глобальный объект для обработчиков событий редактирования
window.WorkerPaymentEditHandlers = {

    /**
     * Инициализация всех обработчиков событий для редактирования
     */
    init: function () {
        this.initClickHandlers();
        this.initChangeHandlers();
        this.initInputHandlers();
        this.initFormSubmitHandler();
        this.initValidationHandlers();

        // NEW: Приводим UI в корректное состояние для уже выбранных типов/секций при загрузке формы
        $('.payment-type-checkbox:checked').each(function () {
            $(this).trigger('change');
        });
        $('#debt-payment-checkbox:checked').trigger('change');

        // Триггерим изменение для уже отмеченных методов оплаты
        $('.payment-method-checkbox:checked').each(function () {
            $(this).trigger('change');
        });
    },

    /**
     * Обработчики кликов для формы редактирования
     */
    initClickHandlers: function () {
        // Делаем весь контейнер кликабельным для чекбокса погашения долга
        $('#debt-payment-section .form-check').on('click', function (e) {
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });

        // Делаем весь контейнер кликабельным для чекбоксов типов платежей
        $('.payment-type-row .form-check').on('click', function (e) {
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });

        // Делаем весь контейнер кликабельным для чекбоксов методов оплаты
        $('.payment-methods-container .form-check').on('click', function (e) {
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                var row = $(this).closest('tr');
                var typeCheckbox = row.find('.payment-type-checkbox');

                if (!typeCheckbox.prop('checked')) {
                    typeCheckbox.prop('checked', true).trigger('change');
                }

                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });
    },

    /**
     * Обработчики изменений для формы редактирования
     */
    initChangeHandlers: function () {
        // Обработчик изменения чекбоксов типов платежей
        $('.payment-type-checkbox').on('change', function () {
            var $this = $(this);
            var row = $this.closest('tr');
            var paymentMethods = row.find('.payment-method-checkbox');
            var amountInput = row.find('.amount-input');
            var dynamicAmounts = row.find('.dynamic-amounts');

            if ($this.prop('checked')) {
                // Включаем методы оплаты и поле суммы
                paymentMethods.prop('disabled', false);
                amountInput.prop('disabled', false);

                // Автоматически выбираем наличные, если ничего не выбрано
                if (!paymentMethods.filter(':checked').length) {
                    paymentMethods.first().prop('checked', true).trigger('change');
                }
            } else {
                // Отключаем и сбрасываем
                paymentMethods.prop('disabled', true).prop('checked', false);
                amountInput.prop('disabled', true).val('');
                dynamicAmounts.hide().empty();

                // Очищаем ошибки валидации
                if (typeof window.WorkerPaymentEditUI !== 'undefined') {
                    window.WorkerPaymentEditUI.clearFieldErrors(amountInput);
                }
            }
        });

        // Обработчик изменения чекбоксов методов оплаты
        $('.payment-method-checkbox').on('change', function () {
            var row = $(this).closest('tr');
            var checkedMethods = row.find('.payment-method-checkbox:checked');
            var amountInput = row.find('.amount-input');
            var dynamicAmounts = row.find('.dynamic-amounts');

            if (checkedMethods.length > 1) {
                // Несколько методов - показываем отдельные поля
                amountInput.hide();
                dynamicAmounts.show();

                if (typeof window.WorkerPaymentEditUI !== 'undefined') {
                    window.WorkerPaymentEditUI.createDynamicAmountInputs(row, checkedMethods);
                }

                // Автоматическое распределение суммы между методами оплаты, как в форме создания
                if (typeof window.WorkerPaymentEditCalculations !== 'undefined') {
                    window.WorkerPaymentEditCalculations.autoDistributeToMethods(row);
                }
            } else if (checkedMethods.length === 1) {
                // Один метод - показываем основное поле
                amountInput.show();
                dynamicAmounts.hide().empty();
            } else {
                // Ни одного метода не выбрано
                amountInput.show().val('');
                dynamicAmounts.hide().empty();
            }
        });

        // Обработчик чекбокса погашения долга
        $('#debt-payment-checkbox').on('change', function () {
            var $this = $(this);
            var details = $('#debt-payment-details');
            var amountInput = $('#debt-payment-amount');

            if ($this.prop('checked')) {
                details.show();
                amountInput.prop('disabled', false);
            } else {
                details.hide();
                amountInput.prop('disabled', true).val('');
            }
        });
    },

    /**
     * Обработчики ввода для формы редактирования
     */
    initInputHandlers: function () {
        // Форматирование числовых полей при вводе
        $(document).on('input', 'input[inputmode="numeric"]', function () {
            var $this = $(this);
            var value = $this.val();

            // Очищаем все символы, кроме цифр, для последующего форматирования
            value = value.replace(/[^\d]/g, '');

            if (typeof window.WorkerPaymentEditCalculations !== 'undefined') {
                // Форматируем число с разделителями тысяч
                var formattedValue = window.WorkerPaymentEditCalculations.formatNumberInput(value);
                $this.val(formattedValue);

                // Дополнительная логика распределения и лимитов
                if ($this.hasClass('amount-input') && !$this.attr('data-dynamic')) {
                    // основное поле
                    window.WorkerPaymentEditCalculations.handleMainAmountInput($this);
                } else if ($this.attr('data-dynamic') === '1') {
                    // динамическое поле в .dynamic-amounts
                    window.WorkerPaymentEditCalculations.simpleAutoDistribute($this);
                } else if ($this.attr('id') === 'debt-payment-amount') {
                    if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                        window.WorkerPaymentEditValidation.validateDebtAmount();
                    }
                }
            }

            // Пересчитываем общую сумму (можно подключить при необходимости)
            // var total = window.WorkerPaymentEditCalculations.calculateTotalAmount();
        });

        // Обработчик потери фокуса для валидации
        $(document).on('blur', 'input[inputmode="numeric"]', function () {
            if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                window.WorkerPaymentEditValidation.validateAmount($(this));
            }
        });

        // Преобразуем отформатированные значения обратно в числа перед отправкой формы
        $('#worker-payment-edit-form').on('submit', function () {
            $('input[inputmode="numeric"]').each(function () {
                if (typeof window.WorkerPaymentEditCalculations !== 'undefined') {
                    var numericValue = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                    $(this).val(numericValue);
                }
            });
        });
    },

    /**
     * Обработчик отправки формы редактирования
     */
    initFormSubmitHandler: function () {
        // Валидация при отправке формы редактирования
        $('#worker-payment-edit-form').on('submit', function (e) {
            e.preventDefault();

            if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                if (!window.WorkerPaymentEditValidation.validateForm()) {
                    return false;
                }
            }

            // Подготавливаем данные для отправки
            var formData;
            if (typeof window.WorkerPaymentEditAjax !== 'undefined') {
                formData = window.WorkerPaymentEditAjax.prepareFormData();
                // Отправляем данные на сервер
                window.WorkerPaymentEditAjax.updatePayment(formData);
            }
        });

        // Обработчик клика по кнопке обновления
        $(document).off('click.worker-payment-update').on('click.worker-payment-update', '.worker-payment-update-button', function (e) {
            e.preventDefault();
            $('#worker-payment-edit-form').trigger('submit');
        });
    },

    /**
     * Обработчики валидации для формы редактирования
     */
    initValidationHandlers: function () {
        // Привязываем валидацию к событиям
        $(document).on('input change', '.payment-amount, .payment-type-checkbox, .payment-method-checkbox', function () {
            // Задержка для избежания частых вызовов
            clearTimeout(window.editValidationTimeout);
            window.editValidationTimeout = setTimeout(function () {
                if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                    window.WorkerPaymentEditValidation.validateForm();
                }
            }, 300);
        });

        // Специальный обработчик для поля погашения долга
        $(document).on('input', '#debt-payment-amount', function () {
            if (typeof window.WorkerPaymentEditValidation !== 'undefined') {
                window.WorkerPaymentEditValidation.validateDebtAmount();
            }
        });
    }
};
